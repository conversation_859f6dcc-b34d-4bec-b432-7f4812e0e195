<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HubSpot Webhook Tester</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        textarea { width: 100%; height: 200px; margin: 10px 0; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; cursor: pointer; }
        button:hover { background: #005a87; }
        .response { background: #f5f5f5; padding: 15px; margin: 10px 0; border-left: 4px solid #007cba; }
        .error { border-left-color: #d32f2f; }
        .success { border-left-color: #388e3c; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
    </style>
</head>
<body>
    <div class="container">
        <h1>HubSpot Webhook Tester</h1>
        <p>Test your HubSpot webhook endpoint at: <code>http://localhost:8080/api/custom_scripts/infinity_hubspot_webhook.php</code></p>
        
        <h3>Test Payload (JSON)</h3>
        <textarea id="payload" placeholder="Enter your test JSON payload here...">
{
  "body": {
    "dealname": "Test Deal from Webhook Tester",
    "email": "<EMAIL>",
    "firstname": "John",
    "lastname": "Doe",
    "hubspot_owner_id": 12345,
    "lead_source": "Website"
  }
}
        </textarea>
        
        <button onclick="sendTestRequest()">Send Test Request</button>
        <button onclick="clearResponse()">Clear Response</button>
        
        <h3>Response</h3>
        <div id="response" class="response">
            <p>Click "Send Test Request" to see the response here...</p>
        </div>
    </div>

    <script>
        async function sendTestRequest() {
            const payload = document.getElementById('payload').value;
            const responseDiv = document.getElementById('response');
            
            try {
                // Parse JSON to validate it
                const jsonData = JSON.parse(payload);
                
                responseDiv.innerHTML = '<p>Sending request...</p>';
                responseDiv.className = 'response';
                
                const response = await fetch('http://localhost:8080/api/custom_scripts/infinity_hubspot_webhook.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-HubSpot-Correlation-ID': 'test-correlation-' + Date.now(),
                        'X-HubSpot-Origin-Hublet': 'na1'
                    },
                    body: payload
                });
                
                const responseText = await response.text();
                
                responseDiv.innerHTML = `
                    <h4>Status: ${response.status} ${response.statusText}</h4>
                    <h4>Response Headers:</h4>
                    <pre>${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}</pre>
                    <h4>Response Body:</h4>
                    <pre>${responseText}</pre>
                `;
                
                if (response.ok) {
                    responseDiv.className = 'response success';
                } else {
                    responseDiv.className = 'response error';
                }
                
            } catch (error) {
                responseDiv.innerHTML = `
                    <h4>Error:</h4>
                    <pre>${error.message}</pre>
                `;
                responseDiv.className = 'response error';
            }
        }
        
        function clearResponse() {
            document.getElementById('response').innerHTML = '<p>Click "Send Test Request" to see the response here...</p>';
            document.getElementById('response').className = 'response';
        }
    </script>
</body>
</html>
