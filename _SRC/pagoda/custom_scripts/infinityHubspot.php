<?php
/*

/Users/<USER>/Infinity/bento/_SERVICES/app/src/api/custom_scripts/infinityHubspot.php

Build script, works for local host docker env- all work done here, but then needs to be copied and added to

/Users/<USER>/Infinity/bento/_SRC/pagoda/custom_scripts/infinityHubspot.php

*/

header('Content-Type: application/json');

// Capture all incoming data
$incomingData = [
    'method' => $_SERVER['REQUEST_METHOD'],
    'headers' => getallheaders(),
    'body' => file_get_contents('php://input'),
    'get_params' => $_GET,
    'post_params' => $_POST,
    'server_info' => [
        'REQUEST_URI' => $_SERVER['REQUEST_URI'],
        'CONTENT_TYPE' => $_SERVER['CONTENT_TYPE'] ?? '',
        'CONTENT_LENGTH' => $_SERVER['CONTENT_LENGTH'] ?? ''
    ]
];

// 1) Forward to Pipedream
$ch = curl_init('https://eo8vqnch968fhpz.m.pipedream.net');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($incomingData));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_exec($ch);
curl_close($ch);

// 2) Forward to MailSpons email
$emailSubject = 'HubSpot Webhook Data';
$emailBody = "HubSpot webhook received:\n\n" . json_encode($incomingData, JSON_PRETTY_PRINT);
mail('<EMAIL>', $emailSubject, $emailBody);

echo json_encode($incomingData, JSON_PRETTY_PRINT);
exit;
?>
