<?php
/*
error_reporting(E_ALL);
ini_set('display_errors', '1');
*/

error_reporting(E_ERROR | E_PARSE);

header("Access-Control-Allow-Origin: *");
header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With");
header('Access-Control-Allow-Credentials: true');

require_once '../apiExample/bento.php';
require_once '../lib/_.php';

// Environment-based API key detection
$apiKey = 'rickyvoltz'; // localhost development
// TODO: Add environment detection for staging/production 'infinity'

// rickyvoltz instance API token
$apiToken = '1ac04c9bfe10b64d1fa9cdff6216035d9795e2a1f46e3fb5df36d6c9a2ec1169fea0618fbc014a0ff8381a75974f1bb46c6e2c1dd9c40ff4db6594a122d9d5ca';
$bento = new BentoAPI($apiKey, $apiToken);

// Read the input stream
$json = file_get_contents("php://input");

// Decode the JSON object
$data = json_decode($json, true);

// Basic HubSpot header validation
$hubspotHeaders = [
    'x-hubspot-correlation-id' => $_SERVER['HTTP_X_HUBSPOT_CORRELATION_ID'] ?? '',
    'x-hubspot-origin-hublet' => $_SERVER['HTTP_X_HUBSPOT_ORIGIN_HUBLET'] ?? ''
];

// Validate this is from HubSpot
if (empty($hubspotHeaders['x-hubspot-correlation-id'])) {
    http_response_code(400);
    exit('Invalid HubSpot request - missing correlation ID');
}

ob_start();

echo " HubSpot processing start :><br>";
echo "<pre> HubSpot payload « \n ", var_dump( json_encode($data , JSON_PRETTY_PRINT) ), "</pre>\n";
echo " HubSpot headers « \n ", var_dump( json_encode($hubspotHeaders , JSON_PRETTY_PRINT) ), "</pre>\n";
echo " <: HubSpot processing end <br>";

$hubspotResp = ob_get_clean();

ob_end_flush();

function httpPost($url, $data)
{
    $curl = curl_init($url);
    curl_setopt($curl, CURLOPT_POST, true);
    curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($curl);
    curl_close($curl);
    return $response;
}

$payload = array(
    "data"=> $hubspotResp
);

/// Development logging to Pipedream
httpPost("https://eodb6i06xps97s9.m.pipedream.net", $payload);

// Extract contact data from Pipedream format
// Expected format: {"body":{"dealname":"...","email":"...","firstname":"...","lastname":"...","hubspot_owner_id":123,"lead_source":"..."}}
$contactData = [];

if (isset($data['body'])) {
    $body = $data['body'];
    $contactData = [
        'hubspot_contact_id' => $body['hubspot_owner_id'] ?? '', // Using owner_id as contact identifier for now
        'email' => $body['email'] ?? '',
        'firstname' => $body['firstname'] ?? '',
        'lastname' => $body['lastname'] ?? '',
        'dealname' => $body['dealname'] ?? '',
        'lead_source' => $body['lead_source'] ?? ''
    ];
} else {
    http_response_code(400);
    exit('Invalid payload format - missing body');
}

// DISCOVERY: Find rickyvoltz object type IDs
echo "<h3>DISCOVERY MODE - rickyvoltz Object Type IDs</h3>";

try {
    // Test API connection with rickyvoltz
    echo "<h4>Testing rickyvoltz API connection...</h4>";

    $testCompanies = $bento->getWhere([
        'objectType' => 'companies',
        'queryObj' => [],
        'paged' => ['limit' => 1]
    ]);

    echo "<h4>✅ rickyvoltz API Connection Successful</h4>";

    // Get contact info types for rickyvoltz
    $contactInfoTypes = $bento->getWhere([
        'objectType' => 'contact_info_types',
        'queryObj' => []
    ]);

    echo "<h4>rickyvoltz Contact Info Types:</h4>";
    foreach($contactInfoTypes as $type) {
        echo "<p>ID: {$type['id']} - Name: {$type['name']}</p>";
    }

    // Get contact types for rickyvoltz
    $contactTypes = $bento->getWhere([
        'objectType' => 'contact_types',
        'queryObj' => []
    ]);

    echo "<h4>rickyvoltz Contact Types:</h4>";
    foreach($contactTypes as $type) {
        echo "<p>ID: {$type['id']} - Name: {$type['name']}</p>";
    }

} catch (Exception $e) {
    echo "<h4>❌ rickyvoltz API Error: " . $e->getMessage() . "</h4>";
}

echo "<h4>Contact Data Extracted:</h4>";
echo "<pre>" . json_encode($contactData, JSON_PRETTY_PRINT) . "</pre>";

http_response_code(200);

?>
