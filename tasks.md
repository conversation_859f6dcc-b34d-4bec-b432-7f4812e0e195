# HubSpot Webhook Integration Tasks

| ID | Task | Status | Feature | Description |
|----|------|--------|---------|-------------|
| T1 | Discover Infinity BentoAPI Token | Todo | F1 | Find API token for Infinity Bento instance by checking codebase patterns, XHR requests, and config files - AVOID Foundation Group values |
| T2 | Discover Infinity Object Type IDs | Todo | F1 | Find correct object type IDs for Companies, Contacts, Email/Phone contact info and tagged_with values specific to Infinity instance |
| T3 | Test Local Development Environment | Todo | F1 | Verify Docker localhost:8080 setup and gulp watch process for development workflow |
| T4 | Analyze Underscore.php Import Pattern | Todo | F1 | Understand proper import patterns for underscore.php in custom scripts by examining existing code |
| T5 | Create Pipedream Test Payload | Todo | F1 | Create test payload based on Pipedream screenshots for development testing scenarios |
| T6 | Create Basic Webhook Endpoint File | Todo | F2 | Create infinity_hubspot_webhook.php with proper structure, headers, and basic validation |
| T7 | Implement Request Parsing | Todo | F2 | Parse incoming HubSpot payload from both Pipedream and direct sources with validation |
| T8 | Implement Contact Deduplication Logic | Todo | F2 | Search for existing contacts by email/name to prevent duplicates using functional programming |
| T9 | Implement Company Creation/Lookup | Todo | F2 | Create or find company records for contact association using discovered type IDs |
| T10 | Implement Contact Creation/Update | Todo | F2 | Create new contacts or update existing ones with proper type IDs and relationships |
| T11 | Implement Contact Info Creation | Todo | F2 | Create email and phone contact info records linked to contact record |
| T12 | Implement System Logging | Todo | F2 | Create comprehensive logging with system notes, Pipedream logs, and MailSpon notifications |
| T13 | Local Development Testing | Todo | F3 | Test webhook endpoint on localhost:8080 with sample payloads and verify database records |
| T14 | Pipedream Integration Testing | Todo | F3 | Test with actual Pipedream webhook using ngrok and verify end-to-end processing |
| T15 | Staging Environment Testing | Todo | F3 | Deploy and test on bento-dev.infinityhospitality.net with staging database |
| T16 | Error Handling Testing | Todo | F3 | Test comprehensive error scenarios including malformed payloads and database failures |
| T17 | Security Review | Todo | F4 | Review code for security best practices including input validation and data exposure |
| T18 | Production Deployment | Todo | F4 | Deploy to production bento.infinityhospitality.net and configure HubSpot webhook |
| T19 | Documentation Completion | Todo | F4 | Complete all documentation including configuration, troubleshooting, and maintenance procedures |

## Features

| ID | Feature | Description |
|----|---------|-------------|
| F1 | Discovery & Environment Setup | Initial discovery of API tokens, object types, and development environment setup |
| F2 | Core Implementation | Main webhook endpoint implementation with parsing, deduplication, and data creation |
| F3 | Testing & Validation | Comprehensive testing across local, staging, and integration environments |
| F4 | Production Deployment | Security review, production deployment, and documentation completion |

## Task Dependencies

**Phase 1 (Discovery)**: T1, T2, T3, T4, T5 (All Independent)
**Phase 2 (Implementation)**: T6 → T7 → T8 → T9 → T10 → T11 → T12
**Phase 3 (Testing)**: T13 → T14 → T15 → T16
**Phase 4 (Production)**: T17 → T18 → T19

## ⚠️ Critical Multi-Tenant Gotchas

### Foundation Group Reference Only
- **NEVER use Foundation Group values**: `foundation_group`, `1636580`, `1763496`, `8bff041c...`
- **Reference patterns only** - fg_salesforce.php shows structure, not values
- **Each tenant has unique IDs** - Infinity will have completely different object type IDs

### Discovery Requirements
- **Task T1**: Must find Infinity-specific API token (not Foundation Group token)
- **Task T2**: Must find Infinity-specific object type IDs and tagged_with values
- **Existing infinity*.php files** may contain hardcoded values - use as reference for discovery

### Development Warnings
- **Multi-tenant architecture** - Same blueprints, different data per instance
- **Instance-aware code** - Always use discovered values, never hardcode
- **Testing isolation** - Infinity changes won't affect Foundation Group

## Notes

- Each task should result in a separate git commit for tracking
- Manual tasks require developer action and documentation of results
- AI-assisted tasks include detailed instructions for implementation
- Dependencies must be completed before starting dependent tasks
- Test each phase thoroughly before proceeding to next phase
