<?php
/*
error_reporting(E_ALL);
ini_set('display_errors', '1');
*/

error_reporting(E_ERROR | E_PARSE);

header("Access-Control-Allow-Origin: *");
header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With");
header('Access-Control-Allow-Credentials: true');

require_once '../apiExample/bento.php';
require_once '../lib/_.php';

// Environment-based API key detection
$apiKey = 'rickyvoltz'; // localhost development
// TODO: Add environment detection for staging/production 'infinity'

// Use the same token as Foundation Group for now - will need to discover Infinity-specific token
$apiToken = '8bff041c171a0b7d5f53f9ad26f26ce4b3c0adf32378c575a5e7a77311e26747a3acda5ae816a0708ae3472460117044fcaf2eca8943ed0b1ce99326ee282747';
$bento = new BentoAPI($apiKey, $apiToken);

// Read the input stream
$json = file_get_contents("php://input");

// Decode the JSON object
$data = json_decode($json, true);

// Basic HubSpot header validation
$hubspotHeaders = [
    'x-hubspot-correlation-id' => $_SERVER['HTTP_X_HUBSPOT_CORRELATION_ID'] ?? '',
    'x-hubspot-origin-hublet' => $_SERVER['HTTP_X_HUBSPOT_ORIGIN_HUBLET'] ?? ''
];

// Validate this is from HubSpot
if (empty($hubspotHeaders['x-hubspot-correlation-id'])) {
    http_response_code(400);
    exit('Invalid HubSpot request - missing correlation ID');
}

ob_start();

echo " HubSpot processing start :><br>";
echo "<pre> HubSpot payload « \n ", var_dump( json_encode($data , JSON_PRETTY_PRINT) ), "</pre>\n";
echo " HubSpot headers « \n ", var_dump( json_encode($hubspotHeaders , JSON_PRETTY_PRINT) ), "</pre>\n";
echo " <: HubSpot processing end <br>";

$hubspotResp = ob_get_clean();

ob_end_flush();

function httpPost($url, $data)
{
    $curl = curl_init($url);
    curl_setopt($curl, CURLOPT_POST, true);
    curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($curl);
    curl_close($curl);
    return $response;
}

$payload = array(
    "data"=> $hubspotResp
);

/// Development logging to Pipedream
httpPost("https://eodb6i06xps97s9.m.pipedream.net", $payload);

// Extract contact data from Pipedream format
// Expected format: {"body":{"dealname":"...","email":"...","firstname":"...","lastname":"...","hubspot_owner_id":123,"lead_source":"..."}}
$contactData = [];

if (isset($data['body'])) {
    $body = $data['body'];
    $contactData = [
        'hubspot_contact_id' => $body['hubspot_owner_id'] ?? '', // Using owner_id as contact identifier for now
        'email' => $body['email'] ?? '',
        'firstname' => $body['firstname'] ?? '',
        'lastname' => $body['lastname'] ?? '',
        'dealname' => $body['dealname'] ?? '',
        'lead_source' => $body['lead_source'] ?? ''
    ];
} else {
    http_response_code(400);
    exit('Invalid payload format - missing body');
}

// DISCOVERY: Find object type IDs for Infinity instance
echo "<h3>DISCOVERY MODE - Finding Infinity Object Type IDs</h3>";

try {
    // Test API connection
    $testCompanies = $bento->getWhere([
        'objectType' => 'companies',
        'queryObj' => [],
        'paged' => ['limit' => 1]
    ]);

    echo "<h4>✅ API Connection Successful</h4>";
    echo "<pre>Sample Company: " . json_encode($testCompanies[0] ?? 'No companies found', JSON_PRETTY_PRINT) . "</pre>";

    // Get contact info types
    $contactInfoTypes = $bento->getWhere([
        'objectType' => 'contact_info_types',
        'queryObj' => []
    ]);

    echo "<h4>Contact Info Types:</h4>";
    foreach($contactInfoTypes as $type) {
        echo "<p>ID: {$type['id']} - Name: {$type['name']}</p>";
    }

    // Get contact types
    $contactTypes = $bento->getWhere([
        'objectType' => 'contact_types',
        'queryObj' => []
    ]);

    echo "<h4>Contact Types:</h4>";
    foreach($contactTypes as $type) {
        echo "<p>ID: {$type['id']} - Name: {$type['name']}</p>";
    }

} catch (Exception $e) {
    echo "<h4>❌ API Error: " . $e->getMessage() . "</h4>";
}

echo "<h4>Contact Data Extracted:</h4>";
echo "<pre>" . json_encode($contactData, JSON_PRETTY_PRINT) . "</pre>";

http_response_code(200);

?>
