<?php
/*
error_reporting(E_ALL);
ini_set('display_errors', '1');
*/

/*

/Users/<USER>/Infinity/bento/_SERVICES/app/src/api/custom_scripts/infinityHubspot.php

Build script, works for local host docker env- all work done here, but then needs to be copied and added to

/Users/<USER>/Infinity/bento/_SRC/pagoda/custom_scripts/infinityHubspot.php

*/


header('Content-Type: application/json');

// Capture all incoming data
$incomingData = [
    'method' => $_SERVER['REQUEST_METHOD'],
    'headers' => getallheaders(),
    'body' => file_get_contents('php://input'),
    'get_params' => $_GET,
    'post_params' => $_POST,
    'server_info' => [
        'REQUEST_URI' => $_SERVER['REQUEST_URI'],
        'CONTENT_TYPE' => $_SERVER['CONTENT_TYPE'] ?? '',
        'CONTENT_LENGTH' => $_SERVER['CONTENT_LENGTH'] ?? ''
    ]
];

echo json_encode($incomingData, JSON_PRETTY_PRINT);
exit;

header("Access-Control-Allow-Origin: *");
header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With, X-HubSpot-Correlation-ID, X-HubSpot-Origin-Hublet");
header('Access-Control-Allow-Credentials: true');

require_once '../apiExample/bento.php';
require_once '../lib/_.php';

// Environment-based API key detection
$apiKey = 'rickyvoltz'; // localhost development
// TODO: Add environment detection for staging/production 'infinity'

// rickyvoltz instance API token
$apiToken = '1ac04c9bfe10b64d1fa9cdff6216035d9795e2a1f46e3fb5df36d6c9a2ec1169fea0618fbc014a0ff8381a75974f1bb46c6e2c1dd9c40ff4db6594a122d9d5ca';
$bento = new BentoAPI($apiKey, $apiToken);

// Read the input stream
$json = file_get_contents("php://input");

// Decode the JSON object
$data = json_decode($json, true);


$hubspotResp = ob_get_clean();

// Don't flush the buffer yet - we'll include it in our JSON response

function httpPost($url, $data)
{
    $curl = curl_init($url);
    curl_setopt($curl, CURLOPT_POST, true);
    curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($curl);
    curl_close($curl);
    return $response;
}

$payload = array(
    "data"=> $hubspotResp
);

/// Development logging to Pipedream
httpPost("https://eodb6i06xps97s9.m.pipedream.net", $payload);

// Extract contact data from Pipedream format
// Expected format: {"body":{"dealname":"...","email":"...","firstname":"...","lastname":"...","hubspot_owner_id":123,"lead_source":"..."}}
$contactData = [];

if (isset($data['body'])) {
    $body = $data['body'];
    $contactData = [
        'hubspot_contact_id' => $body['hubspot_owner_id'] ?? '', // Using owner_id as contact identifier for now
        'email' => $body['email'] ?? '',
        'firstname' => $body['firstname'] ?? '',
        'lastname' => $body['lastname'] ?? '',
        'dealname' => $body['dealname'] ?? '',
        'lead_source' => $body['lead_source'] ?? ''
    ];
} else {
    http_response_code(400);
    exit('Invalid payload format - missing body');
}

// DISCOVERY: Find rickyvoltz object type IDs (captured for debug info)
$discoveryLog = "";

try {
    // Test API connection with rickyvoltz
    $discoveryLog .= "Testing rickyvoltz API connection...\n";

    $testCompanies = $bento->getWhere([
        'objectType' => 'companies',
        'queryObj' => [],
        'paged' => ['limit' => 1]
    ]);

    $discoveryLog .= "✅ rickyvoltz API Connection Successful\n";

    // Get contact info types for rickyvoltz
    $contactInfoTypes = $bento->getWhere([
        'objectType' => 'contact_info_types',
        'queryObj' => []
    ]);

    $discoveryLog .= "rickyvoltz Contact Info Types:\n";
    foreach($contactInfoTypes as $type) {
        $discoveryLog .= "ID: {$type['id']} - Name: {$type['name']}\n";
    }

    // Get contact types for rickyvoltz
    $contactTypes = $bento->getWhere([
        'objectType' => 'contact_types',
        'queryObj' => []
    ]);

    $discoveryLog .= "rickyvoltz Contact Types:\n";
    foreach($contactTypes as $type) {
        $discoveryLog .= "ID: {$type['id']} - Name: {$type['name']}\n";
    }

} catch (Exception $e) {
    $discoveryLog .= "❌ rickyvoltz API Error: " . $e->getMessage() . "\n";
}

// Prepare response for XHR debugging
$response = [
    'status' => 'success',
    'message' => 'HubSpot webhook processed successfully',
    'timestamp' => date('Y-m-d H:i:s'),
    'correlation_id' => $hubspotHeaders['x-hubspot-correlation-id'],
    'processed_data' => $contactData,
    'debug_info' => [
        'raw_payload' => $data,
        'headers' => $hubspotHeaders,
        'api_key' => $apiKey,
        'processing_log' => $hubspotResp,
        'discovery_log' => $discoveryLog
    ]
];

// Set JSON content type for proper XHR response
header('Content-Type: application/json');
http_response_code(200);

// Return JSON response
echo json_encode($response, JSON_PRETTY_PRINT);

?>
