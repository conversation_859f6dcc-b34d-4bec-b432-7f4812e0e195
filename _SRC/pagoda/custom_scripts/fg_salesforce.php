<?php
/*
error_reporting(E_ALL);
ini_set('display_errors', '1');
*/

echo();
die();
error_reporting(E_ERROR | E_PARSE);

header("Access-Control-Allow-Origin: *");
header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With");
header('Access-Control-Allow-Credentials: true');

require_once '../apiExample/bento.php';
require_once '../lib/_.php';


$apiKey = $_REQUEST['pagodaAPIKey'];
$apiKey = 'foundation_group';
$bento = new BentoAPI($apiKey, '8bff041c171a0b7d5f53f9ad26f26ce4b3c0adf32378c575a5e7a77311e26747a3acda5ae816a0708ae3472460117044fcaf2eca8943ed0b1ce99326ee282747');

// Read the input stream
$json = file_get_contents("php://input");

// Decode the JSON object
$data = json_decode($json, true);

ob_start();

echo " start :><br>";
echo "<pre> salesforce response « \n ", var_dump( json_encode($data , JSON_PRETTY_PRINT) ), "</pre>\n";
echo " <: end <br>";

$salesforceResp = ob_get_clean();

ob_end_flush();

function httpPost($url, $data)
{
    $curl = curl_init($url);
    curl_setopt($curl, CURLOPT_POST, true);
    curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($curl);
    curl_close($curl);
    return $response;
}

$payload = array(
    "data"=> $salesforceResp
);
/// _SRC/pagoda/custom_scripts/fg_salesforce.php
httpPost("https://eoskrd6yth6uopj.m.pipedream.net", $payload);


///Salesforce Payload - Updated 04/29/21
// $data = [
// 	"Opportunity Products"=>[
// 		[
// 			"Sales Price"=>1500.00
// 			,"Product Name"=>"Form 990 (Long form)"
// 			,"Tax Year"=>"2020"
// 		]
// 	]
// 	,"Account Id"=>"test"
// 	,"Opportunity Id"=>"test"
// 	,"Fiscal Year"=>"2021"///deprecated-using 'Tax Year' property on Products
// 	,"Product"=>"990-Long form"
// 	,"Account"=>[
// 		"Actual Gross Revenue"=>300000
// 		,"CSM"=>"Cameron Fry"
// 		,"Date Incorporated"=>null
// 		,"Charity Status"=>null
// 		,"FEIN"=>"test"
// 		,"501(c)"=>"4"
// 		,"Date IRS Approved"=>"2016-11-15"
// 		,"OrganizationName"=>"The Erik Hendricks Memorial Foundation, Inc."
// 		,"Public/Private"=>null
// 		,"State Incorporated"=>"DE"
// 		,"Acct End Month"=>12
// 		,"Charity Type"=>"Social Welfare Organization"
// 		,"Description"=>"Mission\r\nThe Climate Mobilization is catalyzing an emergency response to the climate crisis at all levels of government.-BOOKKEEPING L2"
// 		,"Date IRS Effective"=>"2014-12-17"
// 	]
// 	,"OpportunityInformation"=>"990LFFY20\r\nRevenue $250,000\r\nFollow Up=> 01/21/2021 2020 990 Due Date=> 5/15/2021\r\nBrenda is Sales Advisor\r\nPIF"
// 	,"OpportunityOwner"=>[
// 		"Sales Person"=>"Brenda Bourgeois"
// 	]
// 	,"Name"=>"The Erik Hendricks Memorial Foundation, Inc."
// 	,"CloseDate"=>"2021-03-31"
// 	,"Contacts"=>[
// 		[
// 			"Phone"=>"(*************"
// 			,"Email"=>"<EMAIL>"
// 			,"Contact Role"=>"Business User"
// 			,"Contact Name"=>"Marina Mails"
// 		]
// 	]
// 	,"SalesPrice"=>1500.00
// ];
// $data = array (
// 	'Opportunity Products' =>
// 	array (
// 	  0 =>
// 	  array (
// 		'Sales Price' => 150.0,
// 		'Product Name' => 'Form 990N',
// 		'Tax Year' => '2021',
// 	  ),
// 	),
// 	'Account Id' => '001i000001Jixa2AAB',
// 	'Opportunity Id' => '0065d00000voFFYAA2',
// 	'Fiscal Year' => '2022',
// 	'Product' => '990-N',
// 	'Account' =>
// 	array (
// 	  'Actual Gross Revenue' => 10000,
// 	  'CSM' => 'Marnie Harris',
// 	  'Date Incorporated' => NULL,
// 	  'Charity Status' => '509(a)(2)',
// 	  'FEIN' => '47-3308309',
// 	  '501(c)' => '3',
// 	  'Date IRS Approved' => '2015-06-01',
// 	  'OrganizationName' => 'The Erik Hendricks Memorial Foundation, Inc.',
// 	  'Public/Private' => 'Public Charity',
// 	  'State Incorporated' => 'PA',
// 	  'Acct End Month' => 12,
// 	  'Charity Type' => 'Charitable – Giving – Domestic',
// 	  'Description' => 'charitable giving to other nonprofits',
// 	  'Date IRS Effective' => '2015-04-01',
// 	),
// 	'OpportunityInformation' => '990NFY21 website sale
//   No verification of revenue
//   Follow up 1/15/2022
//   990 Due Date 5/15/2022',
// 	'OpportunityOwner' =>
// 	array (
// 	  'Sales Person' => 'Marnie Harris',
// 	),
// 	'Name' => 'The Erik Hendricks Memorial Foundation, Inc. - Compliance',
// 	'CloseDate' => '2022-04-18',
// 	'Contacts' =>
// 	array (
// 	  0 =>
// 	  array (
// 		'Phone' => '(*************',
// 		'Email' => '<EMAIL>',
// 		'Contact Role' => '990 Primary Contact',
// 		'Contact Name' => 'Stephanie Hendricks',
// 	  ),
// 	),
// 	'SalesPrice' => 150.0,
// );

//init salesperson user - search only
$salesperson = $bento->getWhere(array(
		'objectType'=>'users'
		, 'queryObj'=>array(
			'name'=>$data['OpportunityOwner']['Sales Person']
		)
	));

//init csm user - search only
$csm = $bento->getWhere(array(
		'objectType'=>'users'
		, 'queryObj'=>array(
			'name'=>$data['Account']['CSM']
		)
	));

///"0011Y00003DrbAtQA" - test Account Id
///data_source_hash - data_source_hash property on Bento objs
$sfacct_id_string = $data['Account Id'];

///********** - crc32("0011Y00003DrbAtQA")
///data_source - data_source Property on Bento Objs
$sfacct_id_hashint = crc32($sfacct_id_string);

$organization = $bento->getWhere(
				array(
					'objectType' => 'companies'
					, 'queryObj' => array(
						'data_source_hash' => $sfacct_id_string
						, 'data_source' => $sfacct_id_hashint
					)
				)
			)[0];

// Check for Other organizations that match by the org name, provided here:
if (empty($organization)) {

	$organization = $bento->getWhere(
		array(
			'objectType' => 'companies'
			, 'queryObj' => array(
				'name' => $data['Account']['OrganizationName']
			)
		)
	)[0];

}

$newOrg_note = $organization['name']. 'found in Bento COP';

// If there is no existing company, create new company obj
if (empty($organization)) {

	$organization_setup = array(
		'objectType'=>'companies'
		, 'objectData'=>array(
			///FG Company Contact Type - 'Non-Profit' 1652262
			'type'=>1652262
			, 'name'=> $data['Account']['OrganizationName']
			///FG HQ - 'id' 1636580
			, 'parent'=> 1636580
			///FG All Company Team - 'id' 1763496
			, 'tagged_with' => array(1636580, 1763496)
			, 'value' => intval( floatval( strval($data['SalesPrice']) )*100 )
			, 'manager' => $csm['0']['id']
			, 'data_source_hash' => $sfacct_id_string
			, 'data_source' => $sfacct_id_hashint
		)
	);

	$org = $bento->create($organization_setup);
	$organization = $org;
	$newOrg_note = $organization['name']. 'was created';

	// Create contact info for the Organization
	$contactInfoObjs = [];
	if (!empty($data['Contacts']['0']['Email'])) {

		array_push($contactInfoObjs, [
			'objectType' => 	'contact_info',
			'objectData' => array(
				'object_id' => 		$org['id'],
				'object_type' => 	'contact_info',
				'name' => 			'Email Address',
				'title' => 			'Email Address',
				'info' => 			$data['Contacts']['0']['Email'],
				'type' => 			1636586,
				'is_primary' => 	'yes'
				)
			]
		);

	}

	if (!empty($data['Contacts']['0']['Phone'])) {

		array_push($contactInfoObjs, [
			'objectType' => 'contact_info',
			'objectData' => array(
				'object_id' => 		$org['id'],
				'object_type' => 	'contact_info',
				'name' => 			'Phone Number',
				'title' => 			'Phone Number',
				'info' => 			$data['Contacts']['0']['Phone'],
				'type' => 			1636587,
				'is_primary' => 	'yes'
			)
		]);

	}

	if (!empty($contactInfoObjs)) {

		$contactInfoIds = [];
		foreach($contactInfoObjs as $info){

			$contactInfoIds[] = $bento->create($info)['id'];

		}

		$bento->update(array(
			'objectType' => 'companies',
			'objectData' => array(
				'id' => 			$org['id'],
				'contact_info' => 	$contactInfoIds
			)
		));

	}

}

//query contacts at organization
$contact_list = $bento->getWhere(
				array(
					'objectType' => 'contacts'
					, 'queryObj' => array(
						'company' => $organization['id']
					)
				)
			);

$contact = array();

if ( !empty($contact_list) ) {

	///each through contact list in search of contact from salesforce payload
	foreach($contact_list as $key => $val){
		if ($val['name'] == $data['Contacts']['0']['Contact Name']) {
			//if contact present in list assign to var
			$contact = $contact_list[$key];
		}
	};
}

// If no contact is found, create from the Salesforce payload
if ( empty($contact) ) {

	//contact not in organzation contact list so will need to create new
	$contact_setup = array(
		'objectType' => 'contacts'
		, 'objectData' => array(
			'type' => 1652260
			, 'company' => $organization['id']
			, 'parent' => $organization['id']
			, 'sales_person' => $salesperson['0']['id']
			, 'manager' => $csm['0']['id']
			/// HQ, All Company Team, Company Id, Salesperson, CSM
			, 'tagged_with' => array(1636580, 1763496, $organization['id'], $salesperson['0']['id'], $csm['0']['id'])
			, 'data_source_hash' => $sfacct_id_string
			, 'data_source' => $sfacct_id_hashint
		)
	);

	//parse name property on FG Bento Contact obj from salesforce payload string val
	if( strpos($data['Contacts']['0']['Contact Name'], " " ) !== false){

	    $contact_name = explode(" ", $data['Contacts']['0']['Contact Name']);

	    $contact_setup['objectData']['name'] = $data['Contacts']['0']['Contact Name'];
	    $contact_setup['objectData']['fname'] = $contact_name[0];
	    $contact_setup['objectData']['lname'] = $contact_name[1];

	}

	//create new contact in FG Instance
	$contact = $bento->create($contact_setup);

}

$contactInfoObjs = [];

if ( !empty($data['Contacts']['0']['Email']) ) {

	array_push($contactInfoObjs, [
		'objectType'=>'contact_info',
		'objectData'=>array(
			'object_id' => $contact['id'],
			'object_type' => 'contact_info',
			'name' => 'Email Address',
			'title' => 'Email Address',
			'info' => $data['Contacts']['0']['Email'],
			'type' => 1636586,
			'is_primary' => 'yes'
			)
		]
	);

}


if ( !empty($data['Contacts']['0']['Phone']) ) {

	array_push($contactInfoObjs, [
		'objectType'=>'contact_info',
		'objectData'=>array(
			'object_id' => $contact['id'],
			'object_type' => 'contact_info',
			'name' => 'Phone Number',
			'title' => 'Phone Number',
			'info' => $data['Contacts']['0']['Phone'],
			'type' => 1636587,
			'is_primary' => 'yes'
		)
	]);

}


if ( !empty($contactInfoObjs) ) {

	$contactInfoIds = [];

	foreach($contactInfoObjs as $info){

		$contactInfoIds[] = $bento->create($info)['id'];

	}

	$bento->update(array(
		'objectType'=>'contacts',
		'objectData'=>array(
			'id'=>$contact['id'],
			'contact_info'=>$contactInfoIds
		)
	));

}

///search for existing opportunity obj
$opportunity = $bento->getWhere(
				array(
					'objectType' => '#30N93o'
					, 'queryObj' => array(
						'_9' => $data['Opportunity Id']
						, '_10' => $sfacct_id_string
					)
				)
			);

///if no existing opportunity, create new opportunity obj
if ( empty($opportunity) ) {

	$opp_noteBody = 'Additional Notes here: ';
	$opp_noteBody .= '<br />';
	$opp_noteBody .= '<br />----------------------';
	$opp_noteBody .= '<br />';
	$opp_noteBody .= '<pre><h5>SalesForce Account UID: '.$data['Account Id'].'</h5>';
	$opp_noteBody .= '<h5>SalesForce Opportunity UID: '.$data['Opportunity Id'] .'</h5>';
	$opp_noteBody .= '<h5>Timestamp: '. date('d F Y, h:i:s A') .'</h5></pre>';

	$opportunity_setup = array(
		///Opportunity Set Object Type - #30N93o
		'objectType'=>'#30N93o'
		, 'objectData' => array(
			'name' => $data['Account']['OrganizationName'].' Opportunity'
			, 'object_bp_type' => '#30N93o'
			, 'parent' => $organization['id']
			, 'tagged_with' => array(1636580, 1763496, $organization['id'], $salesperson['0']['id'], $csm['0']['id'], $contact['id'])
			, '_1' => $data['CloseDate']
			, '_2' => $data['OpportunityInformation']
			, '_3' => $salesperson['0']['id']
			, '_4' => $data['Product']
			, '_6' => intval( floatval( strval($data['SalesPrice']) )*100 )
			, '_7' => $contact['id']
			, '_8' => $opp_noteBody
			, '_9' => $data['Opportunity Id']
			, '_10' => $sfacct_id_string
		)
	);

	$opp = $bento->create($opportunity_setup);
	$opportunity = array(
		'0'=> $opp
	);
}

///check for core demographic in system
$core_demographic = $bento->getWhere(
				array(
					'objectType' => '#ml7laG'
					, 'queryObj' => array(
						'_36' => $data['Opportunity Id']
					)
				)
			);

// Check for Core Demo by parent, if can't be found by Opportunity ID
if (empty($core_demographic) && is_array($organization)) {

	$core_demographic = $bento->getWhere(
		array(
			'objectType' => '#ml7laG'
			, 'queryObj' => array(
				'parent' => $organization['id']
			)
		)
	);

}

$state_inc = array(
	'add2' => ''
	, 'city' => ''
	, 'country' => 'US'
	, 'street' => ''
	, 'zip' => ''
	, 'state' => $data['Account']['State Incorporated']
);

///if no core demographic then create a new one
if ( empty($core_demographic)) {

	$core_blueprint = $bento->getBlueprint('#ml7laG');

	$bp_options =  $core_blueprint['_3']['options']['options'];

	$accountType = __::find($bp_options, function($option) use($data){

		if ( $option['name']==$data['Account']['501(c)'] && !$option['is_archived'] ){

			return true;
		} else {
			return false;
		}

	});

	$charity_options = $core_blueprint['_4']['options']['options'];

	$accountPubPri = __::find($charity_options, function($option) use($data){

		if ( $option['name']==$data['Account']['Public/Private'] && !$option['is_archived'] ){

			return true;
		} else {
			return false;
		}

	});

	$charity_bp_type_options = $core_blueprint;

	$charity_type_options = __::find($charity_bp_type_options, function($option) use($accountType){

		if ( $option['name']=='Charity Type - '.$accountType['name'] && !$option['is_archived'] ){

			return true;
		} else {
			return false;
		}

	});

	$charity_type = __::find($charity_type_options['options']['options'], function($option) use($data){

		if ( $option['name']==$data['Account']['Charity Type'] && !$option['is_archived']){

			return true;
		} else {
			return false;
		}

	});

	$charity_type_key = '';

	foreach ($charity_bp_type_options as $key=>$value)
	{

		if ( $value['name']=='Charity Type - '.$accountType['name'] ){

			$charity_type_key .= $key;
		}
	}

	$charity_status = '';

	if ($accountType['value'] == 3) {

		$charity_status_options = $core_blueprint['_20']['options']['options'];

		$charity_status = __::find($charity_status_options, function($option) use($data){

			if ( $option['name']==$data['Account']['Charity Status'] && !$option['is_archived'] ){
				return true;
			} else {
				return false;
			}

		});

	}

	$core_demographic_setup = array(
		///Core Demographic - {id:2182974, object_bp_type: 'entity_type', bp_name:'ml7laG'}
		'objectType' => '#ml7laG'
		, 'objectData' => array(
			'name' => $data['Account']['OrganizationName']
			, 'tagged_with' => array(1636580, 1763496, $organization['id'], $contact['id'])
			, 'parent' => $organization['id']
			, '_1' => $data['Account']['Description']
			, '_2' => $data['Account']['FEIN']
			, '_3' => $accountType['value']
			, '_4' => $accountPubPri['value']
			, $charity_type_key => $charity_type['value']
			, '_20' => $charity_status['value']
			, '_21' => $data['Account']['Acct End Month']
			, '_22' => intval( floatval( strval($data['Account']['Actual Gross Revenue']) )*100 )
			, '_23' => $state_inc
			, '_24' => $data['Account']['Date Incorporated']
			, '_25' => $data['Account']['Date IRS Approved']
			, '_26' => $data['Account']['Date IRS Effective']
			, '_27' => $csm['0']['id']
			, '_28' => $salesperson['0']['id']
			, '_31' => $data['Account']['OrganizationName']
			, '_36' => $data['Opportunity Id']
			, '_38' => $data['OpportunityInformation']
		)
	);

	$core_demo = $bento->create($core_demographic_setup);

	$core_demographic = array(
		'0'=> $core_demo
	);

}

$coreServiceProducts = [];
$coreserviceNote = '<h5>Core Services/Products Added:</h5>';

// !TODO: Refactor this to product a single Core Service, with each item selected
if ( !empty($data['Opportunity Products']) ) {

	foreach($data['Opportunity Products'] as $service){

		$coreserviceNote .= '<h5>- '.$service['Tax Year'].' '.$service['Product Name'].'</h5>';

		array_push($coreServiceProducts, [
				'objectType' => 		'#3KelnN'
				, 'objectData' => array(
					'name' => 		$organization['name'] .' | '. $service['Tax Year'] .' | '. $service['Product Name']
					, 'tagged_with' => 	array(1636580, 1763496, $organization['id'], $contact['id'], $salesperson['0']['id'], $csm['0']['id'], $opportunity[0]['id'])
					, 'parent' => 		$opportunity[0]['id']
					, '_1' => 		$service['Tax Year']
					, '_8' => 		1
					, '_11'=> 		$contact['id']
					, '_14' =>   		$csm['0']['id']
					, '_18' => 		$state_inc
					, '_19' => 		intval( floatval( strval( $service['Sales Price'] ) )*100 )
					, '_20' => 		$opportunity[0]['id']
					, '_21' => 		$data['OpportunityInformation']
					, '_58' => 		$data['CloseDate']
					, '_61' => 		$organization['id']
				)
			]
		);
	}

} else {

	array_push($coreServiceProducts, [
			'objectType' => 		'#3KelnN'
			, 'objectData' => array(
				'name' => 		$organization['name'] .' | '. $service['Tax Year']
				, 'tagged_with' => 	array(1636580, 1763496, $organization['id'], $contact['id'], $salesperson['0']['id'], $csm['0']['id'], $opportunity[0]['id'])
				, 'parent' => 		$opportunity[0]['id']
				, '_1' => 		$service['Tax Year']
				, '_8' => 		1
				, '_11'=> 		$contact['id']
				, '_14' =>   		$csm['0']['id']
				, '_18' => 		$state_inc
				, '_19' => 		intval( floatval( strval( $service['Sales Price'] ) )*100 )
				, '_20' => 		$opportunity[0]['id']
				, '_21' => 		$data['OpportunityInformation']
				, '_58' => 		$data['CloseDate']
				, '_61' => 		$organization['id']
			)
		]
	);

}

foreach($coreServiceProducts as $serv){

	$bento->create($serv);

}

$noteBody = '<h5><strong>New Salesforce Submission:</strong></h5>';
$noteBody .= '<h5>Account Id: '.$data['Account Id'].'</h5>';
$noteBody .= '<h5>Opportunity Id: '.$data['Opportunity Id'].'</h5>';
$noteBody .= '<h5>Product: '.$data['Product'].'</h5>';
$noteBody .= $coreserviceNote;
$noteBody .= '<h5>Actual Gross Revenue: $'.$data['Account']['Actual Gross Revenue'].'</h5>';
$noteBody .= '<h5>Date Incorporated: '.$data['Account']['Date Incorporated'].'</h5>';
$noteBody .= '<h5>Charity Status: '.$data['Account']['Charity Status'].'</h5>';
$noteBody .= '<h5>FEIN: '.$data['Account']['FEIN'].'</h5>';
$noteBody .= '<h5>501(c): '.$data['Account']['501(c)'].'</h5>';
$noteBody .= '<h5>OrganizationName: '.$data['Account']['OrganizationName'].'</h5>';
$noteBody .= '<h5>Public/Private: '.$data['Account']['Public/Private'].'</h5>';
$noteBody .= '<h5>State Incorporated: '.$data['Account']['State Incorporated'].'</h5>';
$noteBody .= '<h5>Acct End Month: '.$data['Account']['Acct End Month'].'</h5>';
$noteBody .= '<h5>Charity Type: '.$data['Account']['Charity Type'].'</h5>';
$noteBody .= '<h5>Description: '.$data['Account']['Description'].'</h5>';
$noteBody .= '<h5>Date IRS Effective: '.$data['Account']['Date IRS Effective'].'</h5>';
$noteBody .= '<h5>OpportunityInformation: '.$data['OpportunityInformation'].'</h5>';
$noteBody .= '<h5>OpportunityOwner: '.$data['OpportunityOwner']['Sales Person'].'</h5>';
$noteBody .= '<h5>CloseDate: '.$data['CloseDate'].'</h5>';
$noteBody .= '<h5>Contacts: '.$data['Contacts'][0]['Contact Name'].'</h5>';
$noteBody .= '<h5>SalesPrice: $'.$data['SalesPrice'].'</h5>';
$noteBody .= '<h5>Timestamp: '. date('d F Y, h:i:s A') .'</h5>';

$note = array(
	'record_type'=> 'log',
	'record_type_name'=> 'System Log',
	'activity_type'=> 'Create',
	'type_id'=> $opportunity[0]['id'],
	'tagged_with'=>array(1636580, 1763496, $organization['id'], $contact['id'], $opportunity[0]['id']),
	'note'=>$noteBody,
	'author'=>0
);

$system_note = $bento->create(array(
	'objectType'=>'notes',
	'objectData'=>$note
));

http_response_code(200);

?>
