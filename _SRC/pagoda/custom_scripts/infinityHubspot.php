<?php
/*

/Users/<USER>/Infinity/bento/_SERVICES/app/src/api/custom_scripts/infinityHubspot.php

Build script, works for local host docker env- all work done here, but then needs to be copied and added to

/Users/<USER>/Infinity/bento/_SRC/pagoda/custom_scripts/infinityHubspot.php

*/

header('Content-Type: application/json');

// Capture all incoming data
$incomingData = [
    'method' => $_SERVER['REQUEST_METHOD'],
    'headers' => getallheaders(),
    'body' => file_get_contents('php://input'),
    'get_params' => $_GET,
    'post_params' => $_POST,
    'server_info' => [
        'REQUEST_URI' => $_SERVER['REQUEST_URI'],
        'CONTENT_TYPE' => $_SERVER['CONTENT_TYPE'] ?? '',
        'CONTENT_LENGTH' => $_SERVER['CONTENT_LENGTH'] ?? ''
    ]
];

echo json_encode($incomingData, J<PERSON><PERSON>_PRETTY_PRINT);
exit;
?>
